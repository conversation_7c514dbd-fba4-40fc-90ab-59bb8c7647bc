import { AppRegistry } from 'react-native';
import mobileAds from 'react-native-google-mobile-ads';
import remoteConfig from '@react-native-firebase/remote-config';
import App from './src/App';
import { name as appName } from './package.json';

// Wrap in try-catch for better error handling
try {
  mobileAds()
    .initialize()
    .then(adapterStatuses => {
      console.log('Mobile Ads initialization complete:', adapterStatuses);
    })
    .catch(error => {
      console.error('Mobile Ads initialization failed:', error);
    });
} catch (error) {
  console.error('Error during Mobile Ads setup:', error);
}
try {
  // Initialize other services here
  remoteConfig()
  .setDefaults({
    upperADNative: 'ca-app-pub-2044352253676532/3970208555',
    upperADBanner: 'ca-app-pub-2044352253676532/2331222627',
    bottomADNative: 'ca-app-pub-2044352253676532/1018140952',
    bottomADBanner: 'ca-app-pub-2044352253676532/3838346612',
    appOpenAd: 'ca-app-pub-2044352253676532/5459254310', // Replace with your actual App Open Ad unit ID
    showBannerAdsOnly: false,
    showAppOpenAds: true,
    hideGraphs: false,
  })
  .then(() => remoteConfig().fetchAndActivate())
  .then(fetchedRemotely => {
    if (fetchedRemotely) {
      console.log('Configs were retrieved from the backend and activated.');
    } else {
      console.log(
        'No configs were fetched from the backend, and the local configs were already activated',
      );
    }
  });
} catch (error) {
  console.error('Error during other service setup:', error);
}

AppRegistry.registerComponent(appName, () => App);
