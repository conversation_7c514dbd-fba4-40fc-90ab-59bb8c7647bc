PODS:
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - EXConstants (17.0.8):
    - ExpoModulesCore
  - Expo (52.0.47):
    - ExpoModulesCore
  - ExpoAsset (11.0.5):
    - ExpoModulesCore
  - ExpoFileSystem (18.0.12):
    - ExpoModulesCore
  - ExpoFont (13.0.4):
    - ExpoModulesCore
  - ExpoKeepAwake (14.0.3):
    - ExpoModulesCore
  - ExpoModulesCore (2.2.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - fast_float (6.1.4)
  - FBLazyVector (0.76.9)
  - Firebase/Analytics (11.13.0):
    - Firebase/Core
  - Firebase/Core (11.13.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.13.0)
  - Firebase/CoreOnly (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - Firebase/RemoteConfig (11.13.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.13.0)
  - FirebaseABTesting (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseAnalytics (11.13.0):
    - FirebaseAnalytics/AdIdSupport (= 11.13.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.13.0):
    - FirebaseCoreInternal (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.13.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfig (11.13.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSharedSwift (11.15.0)
  - fmt (11.0.2)
  - glog (0.3.5)
  - Google-Mobile-Ads-SDK (12.2.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - GoogleAppMeasurement (11.13.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.13.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.13.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleUserMessagingPlatform (2.7.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.76.9):
    - hermes-engine/Pre-built (= 0.76.9)
  - hermes-engine/Pre-built (0.76.9)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Default (= 2024.10.14.00)
  - RCT-Folly/Default (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCT-Folly/Fabric (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCTDeprecation (0.76.9)
  - RCTRequired (0.76.9)
  - RCTTypeSafety (0.76.9):
    - FBLazyVector (= 0.76.9)
    - RCTRequired (= 0.76.9)
    - React-Core (= 0.76.9)
  - React (0.76.9):
    - React-Core (= 0.76.9)
    - React-Core/DevSupport (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-RCTActionSheet (= 0.76.9)
    - React-RCTAnimation (= 0.76.9)
    - React-RCTBlob (= 0.76.9)
    - React-RCTImage (= 0.76.9)
    - React-RCTLinking (= 0.76.9)
    - React-RCTNetwork (= 0.76.9)
    - React-RCTSettings (= 0.76.9)
    - React-RCTText (= 0.76.9)
    - React-RCTVibration (= 0.76.9)
  - React-callinvoker (0.76.9)
  - React-Core (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - RCTTypeSafety
    - React-Core/CoreModulesHeaders
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage
    - ReactCodegen
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-jsinspector
    - React-logger
    - React-perflogger
    - React-runtimeexecutor
    - React-timing
  - React-debug (0.76.9)
  - React-defaultsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-domnativemodule
    - React-Fabric
    - React-featureflags
    - React-featureflagsnativemodule
    - React-graphics
    - React-idlecallbacksnativemodule
    - React-ImageManager
    - React-microtasksnativemodule
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-domnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.76.9)
    - React-Fabric/attributedstring (= 0.76.9)
    - React-Fabric/componentregistry (= 0.76.9)
    - React-Fabric/componentregistrynative (= 0.76.9)
    - React-Fabric/components (= 0.76.9)
    - React-Fabric/core (= 0.76.9)
    - React-Fabric/dom (= 0.76.9)
    - React-Fabric/imagemanager (= 0.76.9)
    - React-Fabric/leakchecker (= 0.76.9)
    - React-Fabric/mounting (= 0.76.9)
    - React-Fabric/observers (= 0.76.9)
    - React-Fabric/scheduler (= 0.76.9)
    - React-Fabric/telemetry (= 0.76.9)
    - React-Fabric/templateprocessor (= 0.76.9)
    - React-Fabric/uimanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.76.9)
    - React-Fabric/components/root (= 0.76.9)
    - React-Fabric/components/view (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.76.9)
    - React-FabricComponents/textlayoutmanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.76.9)
    - React-FabricComponents/components/iostextinput (= 0.76.9)
    - React-FabricComponents/components/modal (= 0.76.9)
    - React-FabricComponents/components/rncore (= 0.76.9)
    - React-FabricComponents/components/safeareaview (= 0.76.9)
    - React-FabricComponents/components/scrollview (= 0.76.9)
    - React-FabricComponents/components/text (= 0.76.9)
    - React-FabricComponents/components/textinput (= 0.76.9)
    - React-FabricComponents/components/unimplementedview (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.76.9)
  - React-featureflagsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-graphics (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-ImageManager (0.76.9):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-debug
    - React-jsi
  - React-jsi (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
  - React-jsiexecutor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-perflogger
  - React-jsinspector (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-jsi
    - React-perflogger
    - React-runtimeexecutor
  - React-jsitracing (0.76.9):
    - React-jsi
  - React-logger (0.76.9):
    - glog
  - React-Mapbuffer (0.76.9):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context (4.12.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-safe-area-context/common (= 4.12.0)
    - react-native-safe-area-context/fabric (= 4.12.0)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/common (4.12.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/fabric (4.12.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.76.9)
  - React-NativeModulesApple (0.76.9):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.76.9):
    - DoubleConversion
    - RCT-Folly (= 2024.10.14.00)
  - React-performancetimeline (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-cxxreact
    - React-timing
  - React-RCTActionSheet (0.76.9):
    - React-Core/RCTActionSheetHeaders (= 0.76.9)
  - React-RCTAnimation (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTAppDelegate (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon
  - React-RCTBlob (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTFabric (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-performancetimeline
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTLinking (0.76.9):
    - React-Core/RCTLinkingHeaders (= 0.76.9)
    - React-jsi (= 0.76.9)
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.76.9)
  - React-RCTNetwork (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTSettings (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTText (0.76.9):
    - React-Core/RCTTextHeaders (= 0.76.9)
    - Yoga
  - React-RCTVibration (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-rendererconsistency (0.76.9)
  - React-rendererdebug (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - React-debug
  - React-rncore (0.76.9)
  - React-RuntimeApple (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.76.9):
    - React-jsi (= 0.76.9)
  - React-RuntimeHermes (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.76.9)
  - React-utils (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-debug
    - React-jsi (= 0.76.9)
  - ReactCodegen (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.76.9):
    - ReactCommon/turbomodule (= 0.76.9)
  - ReactCommon/turbomodule (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi
    - React-logger
    - React-perflogger
    - ReactCommon/turbomodule/bridging (= 0.76.9)
    - ReactCommon/turbomodule/core (= 0.76.9)
  - ReactCommon/turbomodule/bridging (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi (= 0.76.9)
    - React-logger
    - React-perflogger
  - ReactCommon/turbomodule/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-debug (= 0.76.9)
    - React-featureflags (= 0.76.9)
    - React-jsi
    - React-logger
    - React-perflogger
    - React-utils (= 0.76.9)
  - RNCAsyncStorage (1.23.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNFBAnalytics (22.2.1):
    - Firebase/Analytics (= 11.13.0)
    - React-Core
    - RNFBApp
  - RNFBApp (22.2.1):
    - Firebase/CoreOnly (= 11.13.0)
    - React-Core
  - RNFBRemoteConfig (22.2.1):
    - Firebase/RemoteConfig (= 11.13.0)
    - React-Core
    - RNFBApp
  - RNGoogleMobileAds (14.11.0):
    - DoubleConversion
    - glog
    - Google-Mobile-Ads-SDK (= 12.2.0)
    - GoogleUserMessagingPlatform (= 2.7.0)
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNIap (12.16.2):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (4.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.4.0)
    - Yoga
  - RNScreens/common (4.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSVG (15.8.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.8.0)
    - Yoga
  - RNSVG/common (15.8.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - Expo (from `../node_modules/expo`)
  - ExpoAsset (from `../node_modules/expo-asset/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../node_modules/expo-font/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBRemoteConfig (from `../node_modules/@react-native-firebase/remote-config`)"
  - RNGoogleMobileAds (from `../node_modules/react-native-google-mobile-ads`)
  - RNIap (from `../node_modules/react-native-iap`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSharedSwift
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  Expo:
    :path: "../node_modules/expo"
  ExpoAsset:
    :path: "../node_modules/expo-asset/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/expo-font/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-11-12-RNv0.76.2-5b4aa20c719830dcf5684832b89a6edb95ac3d64
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBRemoteConfig:
    :path: "../node_modules/@react-native-firebase/remote-config"
  RNGoogleMobileAds:
    :path: "../node_modules/react-native-google-mobile-ads"
  RNIap:
    :path: "../node_modules/react-native-iap"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 1dca942403ed9342f98334bf4c3621f011aa7946
  DoubleConversion: f16ae600a246532c4020132d54af21d0ddb2a385
  EXConstants: fcfc75800824ac2d5c592b5bc74130bad17b146b
  Expo: 1687edb10c76b0c0f135306d6ae245379f50ed54
  ExpoAsset: 48386d40d53a8c1738929b3ed509bcad595b5516
  ExpoFileSystem: 42d363d3b96f9afab980dcef60d5657a4443c655
  ExpoFont: f354e926f8feae5e831ec8087f36652b44a0b188
  ExpoKeepAwake: b0171a73665bfcefcfcc311742a72a956e6aa680
  ExpoModulesCore: c3e810e874b33139221bea656743f106a2826f47
  fast_float: 06eeec4fe712a76acc9376682e4808b05ce978b6
  FBLazyVector: 7605ea4810e0e10ae4815292433c09bf4324ba45
  Firebase: 3435bc66b4d494c2f22c79fd3aae4c1db6662327
  FirebaseABTesting: 4048f61cc10d2fad064d3089ace6bd5fb910169b
  FirebaseAnalytics: 630349facf4a114a0977e5d7570e104261973287
  FirebaseCore: c692c7f1c75305ab6aff2b367f25e11d73aa8bd0
  FirebaseCoreInternal: 29d7b3af4aaf0b8f3ed20b568c13df399b06f68c
  FirebaseInstallations: 0ee9074f2c1e86561ace168ee1470dc67aabaf02
  FirebaseRemoteConfig: 518ca257cdb2ccbc2b781ef2f2104f1104c7488f
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSharedSwift: e17c654ef1f1a616b0b33054e663ad1035c8fd40
  fmt: 01b82d4ca6470831d1cc0852a1af644be019e8f6
  glog: 08b301085f15bcbb6ff8632a8ebaf239aae04e6a
  Google-Mobile-Ads-SDK: 1dfb0c3cb46c7e2b00b0f4de74a1e06d9ea25d67
  GoogleAppMeasurement: 0dfca1a4b534d123de3945e28f77869d10d0d600
  GoogleUserMessagingPlatform: a8b56893477f67212fbc8411c139e61d463349f5
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  hermes-engine: 9e868dc7be781364296d6ee2f56d0c1a9ef0bb11
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: ea9d9256ba7f9322ef911169a9f696e5857b9e17
  RCTDeprecation: ebe712bb05077934b16c6bf25228bdec34b64f83
  RCTRequired: ca91e5dd26b64f577b528044c962baf171c6b716
  RCTTypeSafety: e7678bd60850ca5a41df9b8dc7154638cb66871f
  React: 4641770499c39f45d4e7cde1eba30e081f9d8a3d
  React-callinvoker: 4bef67b5c7f3f68db5929ab6a4d44b8a002998ea
  React-Core: a68cea3e762814e60ecc3fa521c7f14c36c99245
  React-CoreModules: d81b1eaf8066add66299bab9d23c9f00c9484c7c
  React-cxxreact: 984f8b1feeca37181d4e95301fcd6f5f6501c6ab
  React-debug: f1956672cf545729afb71ef838a9789c2a9088f5
  React-defaultsnativemodule: 3e1078bee3f84cae075f625d665bb955cbbf366e
  React-domnativemodule: b0340165a791a9324d0447a050b643f336fdc82b
  React-Fabric: f637bbb636e3348a66d269f179cd39be1d31ee3d
  React-FabricComponents: 9542268e7cc59a49f0c2c1906d8ab9729a979b5a
  React-FabricImage: e2f72efe22e23ff3740502553614f1c2d86132c9
  React-featureflags: dc9c91fcd6a75cc95bd7fec184d0be8ad0f26db5
  React-featureflagsnativemodule: 954d6631b4dad0b84bca72c4a8492c44fad92f2c
  React-graphics: 85866d95589b775bcb5f02946a96e948f53088f1
  React-hermes: ac0bcba26a5d288ebc99b500e1097da2d0297ddf
  React-idlecallbacksnativemodule: 5a357e35e5a3986faf1b3c46a1996c12075f7946
  React-ImageManager: 5b3843bea322e4884400f1d78cfde7c435c374bc
  React-jserrorhandler: 23161cf9fef2da6d56bf5cfda14f4abc8d62b286
  React-jsi: 496fa2b9d63b726aeb07d0ac800064617d71211d
  React-jsiexecutor: dd22ab48371b80f37a0a30d0e8915b6d0f43a893
  React-jsinspector: 3f8d148785a57f082e71382657c5fbe1be75bcdf
  React-jsitracing: 69456514c86311eb042d824e4cf69f9b524b5b97
  React-logger: c4052eb941cca9a097ef01b59543a656dc088559
  React-Mapbuffer: 9343a5c14536d4463c80f09a960653d754daae21
  React-microtasksnativemodule: c7cafd8f4470cf8a4578ee605daa4c74d3278bf8
  react-native-safe-area-context: 3ec0dbf4635e3344cb1303d15fcc4a22876b3e59
  React-nativeconfig: 415626a63057638759bcc75e0a96e2e07771a479
  React-NativeModulesApple: d33b55553c6957ff94835574636838d78121a1c6
  React-perflogger: 72e653eb3aba9122f9e57cf012d22d2486f33358
  React-performancetimeline: f831af5af289407b87cdfbceafb7623e5960f9f3
  React-RCTActionSheet: aacf2375084dea6e7c221f4a727e579f732ff342
  React-RCTAnimation: 395ab53fd064dff81507c15efb781c8684d9a585
  React-RCTAppDelegate: 345a6f1b82abc578437df0ce7e9c48740eca827c
  React-RCTBlob: 13311e554c1a367de063c10ee7c5e6573b2dd1d6
  React-RCTFabric: 192521b666eabe9dbe9eb6f8d72ba4616e189199
  React-RCTImage: 1b1f914bcc12187c49ba5d949dac38c2eb9f5cc8
  React-RCTLinking: 4ac7c42beb65e36fba0376f3498f3cd8dd0be7fa
  React-RCTNetwork: 938902773add4381e84426a7aa17a2414f5f94f7
  React-RCTSettings: e848f1ba17a7a18479cf5a31d28145f567da8223
  React-RCTText: 7e98fafdde7d29e888b80f0b35544e0cb07913cf
  React-RCTVibration: cd7d80affd97dc7afa62f9acd491419558b64b78
  React-rendererconsistency: b89c074e22ca2b9eaa8013e1310c69db85364893
  React-rendererdebug: 25290691d85200d9d229f2c41c24b285a9f04e4b
  React-rncore: 2a6efd857e10a3b299f8a7fe912fd332aa7e515a
  React-RuntimeApple: 383f3b59440644f555d0a475379c5ee97c3173f6
  React-RuntimeCore: 753cb0bcae285509787d465d176c074ef7a6c537
  React-runtimeexecutor: 877596f82f5632d073e121cba2d2084b76a76899
  React-RuntimeHermes: bad4e404e8a6c25bc6ebcfd5eccb671bed76a67d
  React-runtimescheduler: a352af9ab3939273ee0e02650cfc1c8ee6e4d0c9
  React-timing: a693c531e5627dcc200fc7286cbbebf73d73469d
  React-utils: 59c5bbbc0e72be22c9d6eceb40afadf9be872819
  ReactCodegen: 049be6309e06c1027544819670913680f2029b8e
  ReactCommon: b2eb96a61b826ff327a773a74357b302cf6da678
  RNCAsyncStorage: 9ff617df4a485f2efba57bdf4face05a41767390
  RNFBAnalytics: 2f451df50833a890974d55a93557da878a85b29e
  RNFBApp: db9c2e6d36fe579ab19b82c0a4a417ff7569db7e
  RNFBRemoteConfig: b000626bcdf226d4829bc86524c811142cf505ec
  RNGoogleMobileAds: 93298b552849a8922738eab045052d784828bdaf
  RNIap: d73cedfb73b396cad75f90f4ed7d64352d6381cf
  RNScreens: 2fe3d999c2dee6ef1d5e863fec0fb6f7de60c45a
  RNSVG: d332ea2a8540218eadc5c8f31c2b58dfba6b14b5
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: 1259c7a8cbaccf7b4c3ddf8ee36ca11be9dee407

PODFILE CHECKSUM: d95181b3a7a433860ffbb5defddaabe98697999c

COCOAPODS: 1.16.2
