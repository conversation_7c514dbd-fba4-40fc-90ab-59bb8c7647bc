import React, { useState, useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import {
  Pressable,
  Modal,
  StyleSheet,
  Animated,
} from 'react-native';
import { iapService } from './services/iapService';
import { useAdStore } from './store/adStore';
import ChargingPowerDisplay from './screens/ChargingPowerDisplay';
import BatteryStats from './screens/BatteryStats';
import RuntimeGraphs from './screens/RuntimeGraphs';
import AboutUsPage from './screens/AboutUs';
import { theme } from './theme';
import MenuButton from './components/MenuButton';
import NavigationMenu from './components/NavigationMenu';
import AppOpenAdManager from './components/ads/AppOpenAd';

const Stack = createNativeStackNavigator<RootStackParamList>();

export type RootStackParamList = {
  ChargingPowerDisplay: undefined;
  BatteryStats: undefined;
  RuntimeGraphs: undefined;
  AboutUs: undefined;
};

  export default function Navigation() {
  const [menuVisible, setMenuVisible] = useState(false);

  const { initializeAdState } = useAdStore();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        await initializeAdState();
        await iapService.init();
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };

    initializeApp();

    return () => {
      try {
        iapService.cleanup();
      } catch (error) {
        console.error('Error cleaning up IAP service:', error);
      }
    };
  }, [initializeAdState]);


  // Animation for menu
  const [menuAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    const animation = Animated.timing(menuAnimation, {
      toValue: menuVisible ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    });
    animation.start();

    return () => {
      animation.stop();
    };
  }, [menuVisible, menuAnimation]);



  return (
    <>
      {/* App Open Ad Manager */}
      <AppOpenAdManager />

      <Modal
        animationType="none"
        transparent={true}
        visible={menuVisible}
        onRequestClose={() => setMenuVisible(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setMenuVisible(false)}
        >
          <Animated.View
            style={[
              styles.menuContainer,
              {
                opacity: menuAnimation,
                transform: [
                  {
                    translateY: menuAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [-20, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            <NavigationMenu setMenuVisible={setMenuVisible} />
          </Animated.View>
        </Pressable>
      </Modal>

      <Stack.Navigator
        initialRouteName="ChargingPowerDisplay"
        screenOptions={{
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerTitleStyle: {
            color: theme.colors.text,
            fontSize: 18,
            fontWeight: '600',
          },
          headerTintColor: theme.colors.primary,
          headerRight: () => <MenuButton setMenuVisible={setMenuVisible} />,
          headerShadowVisible: false,
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen
          name="ChargingPowerDisplay"
          component={ChargingPowerDisplay}
          options={{
            title: 'Battery Charger',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="BatteryStats"
          component={BatteryStats}
          options={{
            title: 'Battery Statistics',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="RuntimeGraphs"
          component={RuntimeGraphs}
          options={{
            title: 'Runtime Graphs',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="AboutUs"
          component={AboutUsPage}
          options={{
            title: 'Discover Our Apps',
            headerTitleAlign: 'center',
          }}
        />
      </Stack.Navigator>
    </>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
  },
  menuContainer: {
    backgroundColor: theme.colors.cardBackground,
    marginTop: 60,
    marginRight: 16,
    marginLeft: 'auto',
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.small,
    width: 220,
    ...theme.shadow.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  menuButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '10', // 10% opacity
    marginRight: theme.spacing.medium,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: theme.colors.primary,
    marginVertical: 2,
  },
});
