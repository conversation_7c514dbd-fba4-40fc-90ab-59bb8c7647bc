import React from 'react';
import {AirbnbRating} from 'react-native-ratings';
import {Linking, StyleSheet, Text} from 'react-native';
import { theme } from '../theme';

export default function AboutUs() {
  const finishRating = (rate: number) => {

    if (rate === 5) {
     const storeUrl = 'market://details?id=com.chargingstats';
        Linking.openURL(storeUrl).catch(() => {
          Linking.openURL('https://play.google.com/store/apps/details?id=com.chargingstats');
        });
    }
  };
  return (
    <>
      <Text style={styles.rateUsTxt}>
        This application is under development by a single developer
      </Text>
      <AirbnbRating
        count={5}
        reviews={['Terrible ', 'Bad ', 'Disappointing ', 'OK ', 'Good ']}
        defaultRating={4}
        onFinishRating={finishRating}
      />
      <Text style={styles.rateUsTxt}>
        Help us reach more people, Rate us 5 stars
      </Text>
    </>
  );
}
const styles = StyleSheet.create({
  rateUsTxt: {
    fontSize: theme.typography.body.fontSize,
        color: theme.colors.textSecondary,
        textAlign: 'center',
        marginBottom: theme.spacing.xlarge,
        lineHeight: 22,
        letterSpacing: theme.typography.body.letterSpacing,
  },
});
