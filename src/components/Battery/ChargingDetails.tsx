import React, {useEffect, useState} from 'react';
import {View, Text, Animated, StyleSheet} from 'react-native';
import {theme} from '../../theme';
import {getBatteryStats} from '../../utils/powerUtils';

function ChargingDetails() {
  const [voltage, setVoltage] = useState(0);
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    const fetchBatteryStats = async () => {
      const stats = await getBatteryStats();

      const newVoltage = parseFloat(stats.voltage.toFixed(2));
      const newCurrent = parseFloat(stats.current.toFixed(2));
      // Calculate power and ensure it has exactly 2 decimal places

      setVoltage(newVoltage);
      setCurrent(newCurrent);

    };

    fetchBatteryStats();
    const interval = setInterval(fetchBatteryStats, 1000);
    return () => clearInterval(interval);
  }, []);

  return (
    <View style={styles.statsContainer}>
      <View style={styles.statCard}>
        <Text style={styles.statLabel}>Voltage</Text>
        <Text style={styles.statValue}>{voltage}</Text>
        <Text style={styles.statUnit}>V</Text>
      </View>

      <View style={styles.statCard}>
        <Text style={styles.statLabel}>Current</Text>
        <Text style={styles.statValue}>{current}</Text>
        <Text style={styles.statUnit}>mA</Text>
      </View>
    </View>
  );
}
const styles = StyleSheet.create({
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginVertical: theme.spacing.large,
  },
  statCard: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.medium,
    width: '48%',
    alignItems: 'center',
    ...theme.shadow.small,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  statLabel: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.small,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
  },
  statUnit: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
});
export default ChargingDetails;
