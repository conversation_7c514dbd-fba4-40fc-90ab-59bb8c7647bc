import React, {useEffect, useState} from 'react';
import {View, Text, Animated, StyleSheet} from 'react-native';
import {theme} from '../../theme';
import {getBatteryStats} from '../../utils/powerUtils';

function PowerGauge() {
  const [power, setPower] = useState(0);
  const [maxPower, setMaxPower] = useState(25);
  const [powerAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    const fetchBatteryStats = async () => {
      const stats = await getBatteryStats();
      // Calculate power and ensure it has exactly 2 decimal places

      const newPower = parseFloat(stats.power.toFixed(2));

      setPower(newPower);

      // Update maxPower if current power is higher
      if (newPower > maxPower) {
        setMaxPower(newPower);
      }

      const animation = Animated.spring(powerAnimation, {
        toValue: newPower,
        friction: 7,
        tension: 40,
        useNativeDriver: false,
      });

      animation.start();

      return () => {
        animation.stop();
      };
    };

    fetchBatteryStats();
    const interval = setInterval(fetchBatteryStats, 1000);
    return () => clearInterval(interval);
  }, [maxPower, powerAnimation]);
  // Calculate power percentage for gauge with dynamic maxPower
  const powerPercentage = Math.min(power / maxPower, 1) * 100;

  // Determine power color based on value
  const getPowerColor = () => {
    if (power < 5) {
      return theme.colors.textSecondary;
    }
    if (power < 10) {
      return theme.colors.secondary;
    }
    if (power < 15) {
      return theme.colors.primary;
    }
    return theme.colors.highlight;
  };
  return (
    <View style={styles.gaugeContainer}>
      <View style={styles.gauge}>
        <View
          style={[
            styles.gaugeLevel,
            {width: `${powerPercentage}%`, backgroundColor: getPowerColor()},
          ]}
        />
      </View>
      <View style={styles.powerValueContainer}>
        <Animated.Text style={[styles.powerValue, {color: getPowerColor()}]}>
          {power.toFixed(2)}
        </Animated.Text>
        <Text style={styles.powerUnit}>W</Text>
      </View>
    </View>
  );
}
const styles = StyleSheet.create({
  gaugeContainer: {
    width: '100%',
    alignItems: 'center',
    marginVertical: theme.spacing.xlarge,
  },
  gauge: {
    width: '100%',
    height: 12,
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.pill,
    overflow: 'hidden',
    marginBottom: theme.spacing.medium,
  },
  gaugeLevel: {
    height: '100%',
    borderRadius: theme.borderRadius.pill,
  },
  powerValueContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  powerValue: {
    fontSize: theme.typography.value.fontSize,
    fontWeight: theme.typography.value.fontWeight as '700',
  },
  powerUnit: {
    fontSize: theme.typography.subheading.fontSize,
    color: theme.colors.textSecondary,
    marginBottom: 8,
    marginLeft: 4,
  },
});
export default PowerGauge;
