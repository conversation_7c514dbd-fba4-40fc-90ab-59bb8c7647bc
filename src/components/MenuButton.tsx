import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { theme } from '../theme';

interface MenuButtonProps {
  setMenuVisible: (visible: boolean) => void;
}

const MenuButton = ({ setMenuVisible }: MenuButtonProps) => (
  <TouchableOpacity
    onPress={() => setMenuVisible(true)}
    style={styles.menuButton}
    activeOpacity={0.7}
  >
    <View style={styles.menuDot} />
    <View style={styles.menuDot} />
    <View style={styles.menuDot} />
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  menuButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '10', // 10% opacity
    marginRight: theme.spacing.medium,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: theme.colors.primary,
    marginVertical: 2,
  },
});

export default MenuButton;
