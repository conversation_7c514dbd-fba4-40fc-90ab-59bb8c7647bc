import React from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  Linking,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { theme } from '../theme';
import { useRemoveAds } from '../hooks/useRemoveAds';

interface NavigationMenuProps {
  setMenuVisible: (visible: boolean) => void;
}

export default function NavigationMenu({ setMenuVisible }: NavigationMenuProps) {
  const navigation = useNavigation();
  const { isAdFree, handleRemoveAds: handleRemoveAdsHook } = useRemoveAds();

  const handleRateApp = () => {
    const storeUrl = 'market://details?id=com.chargingstats';
    Linking.openURL(storeUrl).catch(() => {
      Linking.openURL('https://play.google.com/store/apps/details?id=com.chargingstats');
    });
    setMenuVisible(false);
  };

  const handleDiscoverApps = () => {
    navigation.navigate('AboutUs' as never);
    setMenuVisible(false);
  };

  const handleRemoveAds = async () => {
    await handleRemoveAdsHook();
    setMenuVisible(false);
  };

  return (
    <>
      <TouchableOpacity
        style={styles.menuItem}
        onPress={handleRateApp}
        activeOpacity={0.7}
      >
        <View style={styles.menuIconContainer}>
          <Text style={styles.menuIcon}>⭐</Text>
        </View>
        <Text style={styles.menuText}>Rate App</Text>
      </TouchableOpacity>

      <View style={styles.menuDivider} />

      <TouchableOpacity
        style={styles.menuItem}
        onPress={handleDiscoverApps}
        activeOpacity={0.7}>
        <View style={styles.menuIconContainer}>
          <Text style={styles.menuIcon}>ℹ️</Text>
        </View>
        <Text style={styles.menuText}>About us</Text>
      </TouchableOpacity>

      <View style={styles.menuDivider} />
      <TouchableOpacity
        style={styles.menuItem}
        onPress={handleRemoveAds}
        activeOpacity={0.7}
      >
        <View style={styles.menuIconContainer}>
          <Text style={styles.menuIcon}>🚫</Text>
        </View>
        <Text style={styles.menuText}>
          {isAdFree ? 'Ads Removed ✓' : 'Remove Ads'}
        </Text>
      </TouchableOpacity>
    </>
  );
}

const styles = StyleSheet.create({
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.medium,
    borderRadius: theme.borderRadius.small,
  },
  menuDivider: {
    height: 1,
    backgroundColor: theme.colors.border,
    marginVertical: theme.spacing.xsmall,
  },
  menuText: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '500',
  },
  menuIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.primary + '20', // 20% opacity
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.medium,
  },
  menuIcon: {
    fontSize: 16,
  },
});