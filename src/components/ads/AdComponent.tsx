import React, {useEffect, useMemo, useState} from 'react';
import {Image, Text, View, StyleSheet} from 'react-native';
import remoteConfig from '@react-native-firebase/remote-config';
import {useAdStore} from '../../store/adStore';


import {
  NativeAd,
  NativeAdView,
  NativeAsset,
  NativeAssetType,
  NativeMediaView,
  BannerAd,
  BannerAdSize,
  NativeMediaAspectRatio,
} from 'react-native-google-mobile-ads';
import {theme} from '../../theme';

interface NativeComponentProps {
  nativeId: string;
  bannerId: string;
  showAdMedia?: boolean;
}

export const AdComponent: React.FC<NativeComponentProps> = ({
  nativeId,
  bannerId,
  showAdMedia,
}) => {
  const [nativeAd, setNativeAd] = useState<NativeAd>();
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const {isAdFree} = useAdStore();
  const isBannerAdsOnly = useMemo(
    () => remoteConfig().getValue('showBannerAdsOnly').asBoolean(),
    [],
  );
  const adContainerStyle = useMemo(
    () => [
      styles.outerContainer,
      { minHeight: showAdMedia ? 450 : 80 },
    ],
    [showAdMedia],
  );

  useEffect(() => {
    setIsLoading(true);
    setHasError(false);

    try {
      NativeAd.createForAdRequest(nativeId, {
        aspectRatio: NativeMediaAspectRatio.SQUARE,
      })
        .then(ad => {
          setNativeAd(ad);
          setHasError(false);
        })
        .catch(error => {
          console.error('Failed to load native ad:', error);
          setHasError(true);
        })
        .finally(() => {
          setIsLoading(false);
        });
    } catch (error) {
      console.error('Error creating native ad request:', error);
      setHasError(true);
      setIsLoading(false);
    }
  }, [nativeId]);

  // Don't render anything if user has ad-free version
  if (isAdFree) {
  return null;
  }

  if (isLoading || hasError || !nativeAd || isBannerAdsOnly) {
    return (
      <View style={styles.adOuterContainer}>
        <View
          style={[styles.outerContainer]}>
            <BannerAd
              unitId={bannerId}
              size={BannerAdSize.MEDIUM_RECTANGLE}
            />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.adOuterContainer}>
      <View style={adContainerStyle}>
        <NativeAdView nativeAd={nativeAd} style={styles.adContainer}>
          <View
            style={[
              styles.mainContainer,
              !showAdMedia && styles.compactContainer,
            ]}>
            <View style={styles.headerContainer}>
              {nativeAd.icon && (
                <NativeAsset assetType={NativeAssetType.ICON}>
                  <Image
                    source={{uri: nativeAd.icon.url}}
                    style={[styles.icon, !showAdMedia && styles.compactIcon]}
                  />
                </NativeAsset>
              )}
              <View style={styles.titleContainer}>
                <NativeAsset assetType={NativeAssetType.HEADLINE}>
                  <Text
                    style={[
                      styles.headline,
                      !showAdMedia && styles.compactHeadline,
                    ]}>
                    {nativeAd.headline}
                  </Text>
                </NativeAsset>
                {nativeAd.advertiser && showAdMedia && (
                  <NativeAsset assetType={NativeAssetType.ADVERTISER}>
                    <Text style={styles.advertiser}>{nativeAd.advertiser}</Text>
                  </NativeAsset>
                )}
                {nativeAd.starRating != null && nativeAd.starRating > 0 && showAdMedia && (
                  <NativeAsset assetType={NativeAssetType.STAR_RATING}>
                    <Text style={styles.rating}>
                      Rating: {nativeAd.starRating.toFixed(1)} ★
                    </Text>
                  </NativeAsset>
                )}
              </View>
              <Text style={styles.sponsoredLabel}>Ad</Text>
            </View>

            {showAdMedia && <NativeMediaView style={styles.mediaView} />}

            {showAdMedia && nativeAd.body && (
              <NativeAsset assetType={NativeAssetType.BODY}>
                <Text style={styles.body}>{nativeAd.body}</Text>
              </NativeAsset>
            )}

            {nativeAd.callToAction && (
              <NativeAsset assetType={NativeAssetType.CALL_TO_ACTION}>
                <View style={styles.ctaButton}>
                  <Text style={styles.ctaText}>{nativeAd.callToAction}</Text>
                </View>
              </NativeAsset>
            )}
          </View>
        </NativeAdView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    width: '100%',
    backgroundColor: 'transparent',
  },
  adContainer: {
    padding: theme.spacing.medium,
    borderRadius: theme.borderRadius.medium,
    ...theme.shadow.small,
    borderWidth: 1,
    borderColor: theme.colors.border,
    width: '100%',
  },
  mainContainer: {
    width: '100%',
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.small,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginBottom: theme.spacing.small,
    minHeight: 60,
    paddingVertical: theme.spacing.small,
    overflow: 'hidden',
  },
  icon: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.medium,
    marginRight: theme.spacing.medium,
  },
  compactIcon: {
    width: 32,
    height: 32,
  },
  titleContainer: {
    flex: 1,
  },
  headline: {
    fontSize: theme.typography.subheading.fontSize,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.xsmall,
    letterSpacing: theme.typography.subheading.letterSpacing,
  },
  compactHeadline: {
    fontSize: theme.typography.body.fontSize,
    marginBottom: 0,
  },
  advertiser: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xsmall,
    letterSpacing: theme.typography.caption.letterSpacing,
  },
  sponsoredLabel: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    backgroundColor: theme.colors.primary + '20', // 20% opacity
    paddingHorizontal: theme.spacing.small,
    paddingVertical: theme.spacing.xsmall,
    borderRadius: theme.borderRadius.pill,
    overflow: 'hidden',
    marginLeft: theme.spacing.small,
  },
  mediaView: {
    width: '100%',
    aspectRatio: 16 / 9,
    maxHeight: 250,
    marginBottom: theme.spacing.medium,
    borderRadius: theme.borderRadius.medium,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  body: {
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.medium,
    marginBottom: theme.spacing.medium,
    lineHeight: 22,
    flexShrink: 1,
    minHeight: 60,
    letterSpacing: theme.typography.body.letterSpacing,
  },
  ctaButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.pill,
    padding: theme.spacing.medium,
    alignItems: 'center',
    marginTop: theme.spacing.medium,
    minWidth: 120,
    ...theme.shadow.small,
  },
  ctaText: {
    color: theme.colors.text,
    fontSize: theme.typography.body.fontSize,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  rating: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.secondary,
    fontWeight: '500',
    marginTop: theme.spacing.xsmall,
  },
  adOuterContainer: {
    marginTop: theme.spacing.medium,
    padding: theme.spacing.small,
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
    ...theme.shadow.small,
  },
});
