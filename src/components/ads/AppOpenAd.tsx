import React, { useEffect, useRef, useCallback, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { AppOpenAd, AdEventType } from 'react-native-google-mobile-ads';
import remoteConfig from '@react-native-firebase/remote-config';
import { useAdStore } from '../../store/adStore';

interface AppOpenAdManagerProps {
  // No props needed - all configuration comes from Remote Config
}

export const AppOpenAdManager: React.FC<AppOpenAdManagerProps> = () => {
  const { isAdFree, isFirstLaunch } = useAdStore();
  const appState = useRef(AppState.currentState);
  const appOpenAdRef = useRef<AppOpenAd | null>(null);
  const isLoadingRef = useRef(false);
  const isShowingRef = useRef(false);
  const showAppOpenAdRef = useRef<(() => Promise<void>) | null>(null);
  const [isRemoteConfigReady, setIsRemoteConfigReady] = useState(false);
  const [isAdLoaded, setIsAdLoaded] = useState(false);
  const [shouldShowOnLoad, setShouldShowOnLoad] = useState(false);
  console.log('========AppOpenAdManager initialized',isFirstLaunch);

  // Initialize remote config
  const initializeRemoteConfig = useCallback(async () => {
    try {
      console.log('============================================================');
      console.log('Initializing remote config for App Open Ad...');

      // Wait a bit to ensure remote config has been fetched and activated
      // This gives time for the fetchAndActivate() call in index.js to complete
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if remote config values are available
      const adUnitId = remoteConfig().getValue('appOpenAd').asString();

      if (adUnitId && adUnitId !== '') {
        console.log('Remote config ready for App Open Ad');
        setIsRemoteConfigReady(true);
      } else {
        console.warn('App Open Ad unit ID not found in remote config, retrying...');
        // Retry after a delay
        setTimeout(() => {
          initializeRemoteConfig();
        }, 3000);
      }
    } catch (error) {
      console.error('Error initializing remote config:', error);
      // Retry after a delay
      setTimeout(() => {
        initializeRemoteConfig();
      }, 5000);
    }
  }, []);

  // Load App Open Ad
  const loadAppOpenAd = useCallback(() => {
    console.log('Load App Open Ad');
    if (isLoadingRef.current || appOpenAdRef.current || !isRemoteConfigReady) {
      return;
    }

    // Get ad unit ID from remote config
    const adUnitId = remoteConfig().getValue('appOpenAd').asString();
    if (!adUnitId || adUnitId === '') {
      console.error('App Open Ad unit ID not found in remote config');
      return;
    }
    console.log('Loading App Open Ad with unit ID:', adUnitId);
    isLoadingRef.current = true;

    const appOpenAd = AppOpenAd.createForAdRequest(adUnitId, {
      requestNonPersonalizedAdsOnly: false,
    });

    appOpenAd.addAdEventListener(AdEventType.LOADED, () => {
      console.log('App Open Ad loaded');
      appOpenAdRef.current = appOpenAd;
      isLoadingRef.current = false;
      setIsAdLoaded(true);

      // Show ad immediately if it should be shown on load
      if (shouldShowOnLoad) {
        setShouldShowOnLoad(false);
        setTimeout(() => {
          showAppOpenAdRef.current?.();
        }, 100);
      }
    });

    appOpenAd.addAdEventListener(AdEventType.ERROR, (error) => {
      console.error('App Open Ad failed to load:', error);
      isLoadingRef.current = false;
      appOpenAdRef.current = null;
      setIsAdLoaded(false);
      setShouldShowOnLoad(false);
    });

    appOpenAd.addAdEventListener(AdEventType.OPENED, () => {
      console.log('App Open Ad opened');
      isShowingRef.current = true;
    });

    appOpenAd.addAdEventListener(AdEventType.CLOSED, () => {
      console.log('App Open Ad closed');
      isShowingRef.current = false;
      appOpenAdRef.current = null;
      setIsAdLoaded(false);
      // Don't automatically preload next ad - only load when needed
    });
    console.log('===========loading ads');
    appOpenAd.load();
  }, [isRemoteConfigReady, shouldShowOnLoad]);

  // Show App Open Ad
  const showAppOpenAd = useCallback(async () => {
    // Don't show if user is ad-free
    if (isAdFree) {
      return;
    }

    // Don't show if this is the first launch
    console.log('=============Checking if this is the first launch:', isFirstLaunch);
    if (isFirstLaunch) {
      console.log('Skipping App Open Ad - first launch');
      return;
    }

    // Don't show if remote config is not ready
    if (!isRemoteConfigReady) {
      console.log('Remote config not ready, skipping App Open Ad');
      return;
    }

    // Check remote config for app open ads
    const isAppOpenAdsEnabled = remoteConfig().getValue('showAppOpenAds').asBoolean();
    if (!isAppOpenAdsEnabled) {
      console.log('App Open Ads disabled via remote config');
      return;
    }

    // Don't show if already showing an ad
    if (isShowingRef.current) {
      return;
    }

    // Show the ad if loaded
    if (appOpenAdRef.current) {
      try {
        await appOpenAdRef.current.show();
      } catch (error) {
        console.error('Error showing App Open Ad:', error);
        appOpenAdRef.current = null;
      }
    } else if (!isLoadingRef.current) {
      // Load ad if not already loading
      loadAppOpenAd();
    }
  }, [isAdFree, isFirstLaunch, isRemoteConfigReady, loadAppOpenAd]);

  // Assign function to ref for use in event listeners
  showAppOpenAdRef.current = showAppOpenAd;

  // Handle app state changes
  const handleAppStateChange = useCallback((nextAppState: AppStateStatus) => {
    if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
      // App has come to the foreground
      console.log('App has come to the foreground');
      showAppOpenAd();
    }
    appState.current = nextAppState;
  }, [showAppOpenAd]);

  useEffect(() => {
    // Initialize remote config first
    console.log('Initializing useEffect for AppOpenAdManager');
    initializeRemoteConfig();

    // Listen for app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
      if (appOpenAdRef.current) {
        appOpenAdRef.current = null;
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Load ad when remote config is ready and user is not ad-free
  useEffect(() => {
    console.log('Load ad when remote config is ready and user is not ad-free');
    if (isRemoteConfigReady && !isAdFree) {
      loadAppOpenAd();
    }
  }, [isRemoteConfigReady, isAdFree, loadAppOpenAd]);

  // Cleanup when user becomes ad-free
  useEffect(() => {
    if (isAdFree && appOpenAdRef.current) {
      appOpenAdRef.current = null;
      isLoadingRef.current = false;
    }
  }, [isAdFree]);

  // Show app open ad on app launch (but not first launch)
  useEffect(() => {
    if (isRemoteConfigReady && !isFirstLaunch && !isAdFree) {
      console.log('App launched - checking if ad should be shown');
      
      // Check remote config for app open ads
      const isAppOpenAdsEnabled = remoteConfig().getValue('showAppOpenAds').asBoolean();
      if (!isAppOpenAdsEnabled) {
        console.log('App Open Ads disabled via remote config');
        return;
      }
      
      if (isAdLoaded && appOpenAdRef.current) {
        // Ad is already loaded, show it immediately
        console.log('Ad is loaded, showing immediately');
        showAppOpenAd();
      } else {
        // Ad is not loaded yet, mark it to be shown when it loads
        console.log('Ad not loaded yet, will show when ready');
        setShouldShowOnLoad(true);
      }
    }
  }, [isRemoteConfigReady, isFirstLaunch, isAdFree, isAdLoaded, showAppOpenAd]);

  // This component doesn't render anything
  return null;
};

export default AppOpenAdManager;
