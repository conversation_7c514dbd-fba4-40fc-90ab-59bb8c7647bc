import React, { useEffect, useMemo, useState } from 'react';
import { View, StyleSheet, Text, Image } from 'react-native';
import {
  BannerAd,
  BannerAdSize,
  NativeAd,
  NativeAdView,
  NativeAsset,
  NativeAssetType,
  NativeMediaAspectRatio,
} from 'react-native-google-mobile-ads';
import remoteConfig from '@react-native-firebase/remote-config';
import { useAdStore } from '../../store/adStore';
import { theme } from '../../theme';

interface TopBannerAdProps {
  bannerId: string;
  nativeId?: string;
}

export const TopBannerAd: React.FC<TopBannerAdProps> = ({ bannerId, nativeId }) => {
  const [nativeAd, setNativeAd] = useState<NativeAd>();
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(!!nativeId);
  const { isAdFree } = useAdStore();
  const isBannerAdsOnly = useMemo(
    () => remoteConfig().getValue('showBannerAdsOnly').asBoolean(),
    [],
  );
  useEffect(() => {
    if (!nativeId) {return;}

    setIsLoading(true);
    setHasError(false);

    try {

      NativeAd.createForAdRequest(nativeId, {
        aspectRatio: NativeMediaAspectRatio.LANDSCAPE,
      })
        .then(ad => {
          setNativeAd(ad);
          setHasError(false);

        })
        .catch(error => {
          console.error('Failed to load native ad:', error);
          setHasError(true);
        })
        .finally(() => {
          setIsLoading(false);
          console.log('Native ad request completed for ID:', nativeId);

        });
    } catch (error) {
      console.error('Error creating native ad request:', error);
      setHasError(true);
      setIsLoading(false);
    }
  }, [nativeId]);

  if (isAdFree) {
    return null;
  }

  if (isLoading) {
    return (
      <View style={[styles.container, { minHeight: 80 }]} />
    );
  }

  // If native ad failed or no nativeId provided, show banner ad
  if (hasError || !nativeAd || !nativeId || isBannerAdsOnly) {
    return (
      <View style={styles.container}>
        <BannerAd
          unitId={bannerId}
          size={BannerAdSize.BANNER}
          requestOptions={{
            requestNonPersonalizedAdsOnly: true,
          }}
        />
      </View>
    );
  }

  // Show native ad
  return (
    <View style={styles.container}>
      <NativeAdView nativeAd={nativeAd} style={styles.nativeAdContainer}>
        <View style={styles.nativeAdContent}>
          <View style={styles.headerContainer}>
            {nativeAd.icon && (
              <NativeAsset assetType={NativeAssetType.ICON}>
                <Image
                  source={{ uri: nativeAd.icon.url }}
                  style={styles.icon}
                />
              </NativeAsset>
            )}
            <View style={styles.titleContainer}>
              <NativeAsset assetType={NativeAssetType.HEADLINE}>
                <Text style={styles.headline} numberOfLines={1}>
                  {nativeAd.headline}
                </Text>
              </NativeAsset>
            </View>
            <Text style={styles.sponsoredLabel}>Ad</Text>
          </View>
          {nativeAd.callToAction && (
            <NativeAsset assetType={NativeAssetType.CALL_TO_ACTION}>
              <View style={styles.ctaButton}>
                <Text style={styles.ctaText}>{nativeAd.callToAction}</Text>
              </View>
            </NativeAsset>
          )}
        </View>
      </NativeAdView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: theme.colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    paddingVertical: theme.spacing.xsmall,
    marginBottom: theme.spacing.small,
    ...theme.shadow.small,
  },
  nativeAdContainer: {
    width: '100%',
    padding: theme.spacing.small,
    borderRadius: theme.borderRadius.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  nativeAdContent: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: theme.spacing.small,
  },
  icon: {
    width: 24,
    height: 24,
    borderRadius: theme.borderRadius.small,
    marginRight: theme.spacing.small,
  },
  titleContainer: {
    flex: 1,
  },
  headline: {
    fontSize: theme.typography.body.fontSize,
    fontWeight: '600',
    color: theme.colors.text,
  },
  sponsoredLabel: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    backgroundColor: theme.colors.primary + '20',
    paddingHorizontal: theme.spacing.xsmall,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.pill,
    overflow: 'hidden',
    marginLeft: theme.spacing.small,
  },
  ctaButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.small,
    paddingHorizontal: theme.spacing.small,
    paddingVertical: theme.spacing.xsmall,
    minWidth: 80,
  },
  ctaText: {
    color: theme.colors.text,
    fontSize: theme.typography.caption.fontSize,
    fontWeight: '600',
    textAlign: 'center',
  },
});
