import { Alert } from 'react-native';
import { useState, useEffect } from 'react';
import { useAdStore } from '../store/adStore';
import { iapService } from '../services/iapService';

export const useRemoveAds = () => {
  const { isAdFree } = useAdStore();
  const [productPrice, setProductPrice] = useState<string>('$1.00');

  // Fetch product price on mount
  useEffect(() => {
    const fetchProductPrice = async () => {
      try {
        const product = await iapService.getRemoveAdsProduct();
        if (product && product.localizedPrice) {
          setProductPrice(product.localizedPrice);
        }
      } catch (error) {
        console.error('Error fetching product price:', error);
        // Keep default price if fetch fails
      }
    };

    fetchProductPrice();
  }, []);

  const handleRemoveAds = async () => {
    try {
      if (isAdFree) {
        Alert.alert('Already Ad-Free', 'You have already removed ads from the app.');
        return;
      }

      // First try to get the product info
      try {
        await iapService.getRemoveAdsProduct();
      } catch (error: any) {
        Alert.alert('Error', error.message || 'Unable to get product information. Please try again later.');
        return;
      }

      // If product info is successful, try to make the purchase
      try {
        await iapService.purchaseRemoveAds();
        // Note: setAdFree is now handled in the purchaseUpdatedListener when purchase is confirmed
      } catch (error: any) {
        Alert.alert('Error', error.message || 'Failed to complete purchase. Please try again later.');
      }
    } catch (error) {
      console.error('Purchase flow error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again later.');
    }
  };

  return {
    isAdFree,
    handleRemoveAds,
    productPrice,
  };
};
