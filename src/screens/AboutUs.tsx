import React from 'react';
import {StyleSheet, View, Dimensions, ScrollView, Text} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import remoteConfig from '@react-native-firebase/remote-config';
import {AdComponent} from '../components/ads/AdComponent';
import {AppCard} from '../components/AppCard';
import {theme} from '../theme';
import AboutUs from '../components/AboutUs';

const {width: screenWidth} = Dimensions.get('window');

export default function AboutUsPage() {
  analytics().logEvent('ads_tab_viewed');

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
        bounces={true}>
        <AdComponent
          nativeId={remoteConfig().getValue('upperADNative').asString()}
          bannerId={remoteConfig().getValue('upperADBanner').asString()}
          showAdMedia={true}
        />

        <View style={styles.spacer} />
          <AboutUs />
        <View style={styles.spacer} />

        {/* Our Other Apps Section */}
        <View style={styles.appsSection}>
          <Text style={styles.appsSectionTitle}>
            🚀 Discover Our Other Apps
          </Text>
          <Text style={styles.appsSectionSubtitle}>
            Explore more amazing tools we've built for you
          </Text>

          <View style={styles.appsGrid}>
            <AppCard
              icon="🎨"
              title="SVG Viewer Pro"
              description="Professional SVG file viewer and editor"
              packageId="com.svgviewerapp"
            />
            <AppCard
              icon="💭"
              title="Friendly Chat"
              description="Meet new people anonymously with no login"
              packageId="com.friendlychatclient"
            />
            <AppCard
              icon="💬"
              title="Quick Chat"
              description="WhatsApp without saving numbers"
              packageId="com.quickchat.plustech"
            />
            <AppCard
              icon="🏦"
              title="Bank Balance"
              description="Check bank balance via SMS"
              packageId="com.banksms"
            />
            <AppCard
              icon="📱"
              title="QR Generator"
              description="Create and scan QR codes instantly"
              packageId="com.pt.qrcodegenerator"
            />
            <AppCard
              icon="📧"
              title="Mail Templates Pro"
              description="Professional email templates"
              packageId="com.instantemailcomposer"
            />
          </View>
        </View>

        <View style={styles.spacer} />
        <AdComponent
          nativeId={remoteConfig().getValue('bottomADNative').asString()}
          bannerId={remoteConfig().getValue('bottomADBanner').asString()}
          showAdMedia={true}
        />

        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
    width: '100%',
  },
  scrollContent: {
    alignItems: 'center',
    paddingVertical: theme.spacing.small,
  },
  loadingContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: theme.spacing.small,
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  errorText: {
    fontSize: theme.typography.subheading.fontSize,
    color: theme.colors.error,
    fontWeight: 'bold',
    marginBottom: theme.spacing.small,
  },
  errorSubtext: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
  },
  adFreeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    paddingHorizontal: theme.spacing.large,
  },
  adFreeText: {
    fontSize: theme.typography.heading.fontSize,
    fontWeight: 'bold',
    color: theme.colors.success,
    marginBottom: theme.spacing.small,
    textAlign: 'center',
  },
  adFreeSubtext: {
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  bannerAd: {
    width: screenWidth,
    alignSelf: 'center',
    marginVertical: theme.spacing.small,
  },
  secondaryBannerAd: {
    width: screenWidth,
    alignSelf: 'center',
  },
  spacer: {
    height: theme.spacing.large,
    backgroundColor: theme.colors.border,
    width: '100%',
  },
  bottomPadding: {
    height: theme.spacing.xxlarge,
    width: '100%',
  },
  // Apps Section Styles
  appsSection: {
    width: '100%',
    paddingHorizontal: theme.spacing.large,
    paddingVertical: theme.spacing.xlarge,
    backgroundColor: theme.colors.cardBackground,
    marginHorizontal: theme.spacing.medium,
    borderRadius: theme.borderRadius.medium,
    ...theme.shadow.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  appsSectionTitle: {
    fontSize: theme.typography.heading.fontSize,
    fontWeight: '700' as const,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.small,
    letterSpacing: theme.typography.heading.letterSpacing,
  },
  appsSectionSubtitle: {
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xlarge,
    lineHeight: 22,
    letterSpacing: theme.typography.body.letterSpacing,
  },
  appsGrid: {
    gap: theme.spacing.medium,
  },
});
