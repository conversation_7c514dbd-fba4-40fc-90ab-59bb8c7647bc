import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  NativeModules,
  TouchableOpacity,
  NativeEventEmitter,
  ScrollView,
  StyleSheet,
  Animated,
} from 'react-native';
import { AD_IDS } from '../utils/Constants';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../Navigation';
import { theme, commonStyles } from '../theme';
import { AdComponent } from '../components/ads/AdComponent';
import { TopBannerAd } from '../components/ads/TopBannerAd';

type Props = NativeStackScreenProps<RootStackParamList, 'BatteryStats'>;

export default function BatteryStats({ navigation }: Props) {
  const [voltage, setVoltage] = useState<number | null>(null);
  const [current, setCurrent] = useState<number | null>(null);
  const [isCharging, setIsCharging] = useState<boolean>(false);
  const [batteryHealth, setBatteryHealth] = useState<string>('Unknown');
  const [deviceModel, setDeviceModel] = useState<string>('');
  const [temperature, setTemperature] = useState<number | null>(null);
  const [cycleCount, setCycleCount] = useState<number | null>(null);

  const [batteryLevel, setBatteryLevel] = useState<number | null>(null);
  const [chargingType, setChargingType] = useState<string>('Unknown');
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [batteryTechnology, setBatteryTechnology] = useState<string>('Unknown');
  const [currentCapacity, setCurrentCapacity] = useState<number | null>(null);
  const [designCapacity, setDesignCapacity] = useState<number | null>(null);

  useEffect(() => {
    const fetchBatteryInfo = async () => {
      try {
        const stats = await NativeModules.BatteryModule.getBatteryStats();
        setVoltage(stats.voltage);
        setCurrent(stats.current);
        setIsCharging(stats.isCharging);
        setDeviceModel(stats.deviceModel);
        setTemperature(stats.temperature);
        setCycleCount(stats.cycleCount);
        setBatteryLevel(stats.batteryLevel);
        setChargingType(stats.chargingType);
        setTimeRemaining(stats.timeRemaining);
        setBatteryTechnology(stats.batteryTechnology);
        setCurrentCapacity(stats.currentCapacity);
        setDesignCapacity(stats.designCapacity);

        // Convert health code to readable status
        type HealthStatus = {
          [key: number]: string;
        };

        const healthStatusMap: HealthStatus = {
          1: 'Unknown',
          2: 'Good',
          3: 'Overheat',
          4: 'Dead',
          5: 'Over voltage',
          6: 'Unspecified failure',
          7: 'Cold',
        };

        const healthStatus = healthStatusMap[stats.health as number] || 'Unknown';

        setBatteryHealth(healthStatus);
      } catch (error) {
        console.error('Error fetching battery info:', error);
        setVoltage(null);
        setCurrent(null);
        setIsCharging(false);
        setBatteryHealth('Unknown');
        setTemperature(null);
        setCycleCount(null);
        setBatteryLevel(null);
        setChargingType('Unknown');
        setTimeRemaining(null);
        setBatteryTechnology('Unknown');
        setCurrentCapacity(null);
        setDesignCapacity(null);
      }
    };

    // Initial fetch
    fetchBatteryInfo();

    // Set up event emitter for battery changes
    const eventEmitter = new NativeEventEmitter(NativeModules.BatteryModule);
    const batteryChangeListener = eventEmitter.addListener('batteryChange', (newStats: any) => {
      setVoltage(newStats.voltage);
      setCurrent(newStats.current);
      setIsCharging(newStats.isCharging);
      setTemperature(newStats.temperature);
      setCycleCount(newStats.cycleCount);
      setBatteryLevel(newStats.batteryLevel);
      setChargingType(newStats.chargingType);
      setTimeRemaining(newStats.timeRemaining);
      setBatteryTechnology(newStats.batteryTechnology);
      setCurrentCapacity(newStats.currentCapacity);
      setDesignCapacity(newStats.designCapacity);

      // Convert health code to readable status
      type HealthStatus = {
        [key: number]: string;
      };

      const healthStatusMap: HealthStatus = {
        1: 'Unknown',
        2: 'Good',
        3: 'Overheat',
        4: 'Dead',
        5: 'Over voltage',
        6: 'Unspecified failure',
        7: 'Cold',
      };

      const healthStatus = healthStatusMap[newStats.health as number] || 'Unknown';
      setBatteryHealth(healthStatus);
    });

    // Register the listener
    NativeModules.BatteryModule.addBatteryChangeListener();

    // Clean up listener on unmount
    return () => {
      batteryChangeListener.remove();
      NativeModules.BatteryModule.removeBatteryChangeListener();
    };
  }, []);



  const formatTimeRemaining = (seconds: number | null) => {
    if (seconds === null || seconds < 0) {return 'N/A';}
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  // Animation for battery level
  const [batteryFillAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    if (batteryLevel !== null) {
      const animation = Animated.timing(batteryFillAnimation, {
        toValue: batteryLevel / 100,
        duration: 1000,
        useNativeDriver: false,
      });

      animation.start();

      return () => {
        animation.stop();
      };
    }
  }, [batteryLevel, batteryFillAnimation]);

  // Get health status color
  const getHealthStatusColor = () => {
    switch (batteryHealth) {
      case 'Good': return theme.colors.success;
      case 'Overheat': return theme.colors.warning;
      case 'Dead': return theme.colors.error;
      case 'Over voltage': return theme.colors.warning;
      case 'Cold': return theme.colors.secondary;
      default: return theme.colors.textSecondary;
    }
  };

  // Get temperature color
  const getTemperatureColor = () => {
    if (temperature === null) {return theme.colors.textSecondary;}
    if (temperature > 45) {return theme.colors.error;}
    if (temperature > 40) {return theme.colors.warning;}
    if (temperature < 10) {return theme.colors.secondary;}
    return theme.colors.success;
  };

  // Get capacity health percentage
  const getCapacityHealthPercentage = () => {
    if (currentCapacity === null || designCapacity === null) {return null;}
    return (currentCapacity / designCapacity) * 100;
  };

  const capacityPercentage = getCapacityHealthPercentage();

  return (
    <View style={commonStyles.container}>
      <TopBannerAd bannerId={AD_IDS.BANNER.BATTERY_STATS} nativeId={AD_IDS.NATIVE.BATTERY_STATS} />
      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ paddingBottom: theme.spacing.xlarge }}>
        <Text style={commonStyles.screenTitle}>Battery Statistics</Text>

        {/* Battery Level Indicator */}
        <View style={styles.batteryContainer}>
          <View style={styles.batteryOutline}>
            <Animated.View
              style={[
                styles.batteryFill,
                {
                  width: batteryFillAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                  backgroundColor: batteryLevel !== null
                    ? batteryLevel > 80
                      ? theme.colors.success
                      : batteryLevel > 30
                        ? theme.colors.secondary
                        : theme.colors.error
                    : theme.colors.textSecondary,
                },
              ]}
            />
          </View>
          <Text style={styles.batteryPercentage}>
            {batteryLevel !== null ? `${batteryLevel}%` : 'N/A'}
          </Text>
          <Text style={styles.batteryStatus}>
            {isCharging ? '⚡ Charging' : '🔋 Discharging'}
          </Text>
        </View>

        <TopBannerAd bannerId={AD_IDS.BANNER.BATTERY_STATS_2} nativeId={AD_IDS.NATIVE.BATTERY_STATS_2} />
        {/* Device Info Card */}
        <View style={commonStyles.card}>
          <Text style={commonStyles.sectionTitle}>Device Information</Text>

          <View style={commonStyles.statRow}>
            <Text style={commonStyles.label}>Device Model</Text>
            <Text style={commonStyles.value}>{deviceModel}</Text>
          </View>

          <View style={commonStyles.statRow}>
            <Text style={commonStyles.label}>Battery Technology</Text>
            <Text style={commonStyles.value}>{batteryTechnology}</Text>
          </View>

          <View style={commonStyles.statRow}>
            <Text style={commonStyles.label}>Charging Type</Text>
            <Text style={commonStyles.value}>{chargingType}</Text>
          </View>
        </View>

        <TopBannerAd bannerId={AD_IDS.BANNER.BATTERY_STATS_3} nativeId={AD_IDS.NATIVE.BATTERY_STATS_3} />
        {/* Battery Health Card */}
        <View style={commonStyles.card}>
          <Text style={commonStyles.sectionTitle}>Battery Health</Text>

          <View style={styles.healthIndicator}>
            <View style={[styles.healthStatus, { backgroundColor: getHealthStatusColor() }]} />
            <Text style={[styles.healthStatusText, { color: getHealthStatusColor() }]}>
              {batteryHealth}
            </Text>
          </View>

          <View style={commonStyles.statRow}>
            <Text style={commonStyles.label}>Temperature</Text>
            <Text style={[commonStyles.value, { color: getTemperatureColor() }]}>
              {temperature !== null ? `${temperature.toFixed(1)}°C` : 'N/A'}
            </Text>
          </View>

          <View style={commonStyles.statRow}>
            <Text style={commonStyles.label}>Cycle Count</Text>
            <Text style={commonStyles.value}>
              {cycleCount !== null ? cycleCount : 'N/A'}
            </Text>
          </View>

          {capacityPercentage !== null && (
            <View style={styles.capacityContainer}>
              <Text style={commonStyles.label}>Battery Capacity Health</Text>
              <View style={styles.capacityBar}>
                <View
                  style={[
                    styles.capacityFill,
                    {
                      width: `${capacityPercentage}%`,
                      backgroundColor: capacityPercentage > 80
                        ? theme.colors.success
                        : capacityPercentage > 60
                          ? theme.colors.secondary
                          : theme.colors.error,
                    },
                  ]}
                />
              </View>
              <Text style={styles.capacityText}>
                {capacityPercentage.toFixed(1)}%
              </Text>
              <Text style={styles.capacityDetails}>
                {currentCapacity} / {designCapacity} mAh
              </Text>
            </View>
          )}
        </View>

        <TopBannerAd bannerId={AD_IDS.BANNER.BATTERY_STATS_4} nativeId={AD_IDS.NATIVE.BATTERY_STATS_4} />
        {/* Current Stats Card */}
        <View style={commonStyles.card}>
          <Text style={commonStyles.sectionTitle}>Current Statistics</Text>

          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={commonStyles.label}>Voltage</Text>
              <Text style={commonStyles.valueHighlight}>
                {voltage !== null ? `${voltage.toFixed(2)}` : 'N/A'}
              </Text>
              <Text style={styles.statUnit}>V</Text>
            </View>

            <View style={styles.statItem}>
              <Text style={commonStyles.label}>Current</Text>
              <Text style={commonStyles.valueHighlight}>
                {current !== null ? `${current.toFixed(0)}` : 'N/A'}
              </Text>
              <Text style={styles.statUnit}>mA</Text>
            </View>

            <View style={styles.statItem}>
              <Text style={commonStyles.label}>Power</Text>
              <Text style={commonStyles.valueHighlight}>
                {voltage !== null && current !== null
                  ? `${((voltage * current) / 1000).toFixed(2)}`
                  : 'N/A'}
              </Text>
              <Text style={styles.statUnit}>W</Text>
            </View>

            <View style={styles.statItem}>
              <Text style={commonStyles.label}>Time Remaining</Text>
              <Text style={commonStyles.valueHighlight}>
                {formatTimeRemaining(timeRemaining)}
              </Text>
              <Text style={styles.statUnit}>
                {isCharging ? 'until full' : 'until empty'}
              </Text>
            </View>
          </View>
        </View>

        <AdComponent
          nativeId={AD_IDS.NATIVE.BATTERY_STATS_5}
          bannerId={AD_IDS.BANNER.BATTERY_STATS_5}
          showAdMedia={true}
        />

        {/* Navigation Button */}
        <TouchableOpacity
          style={[commonStyles.button, { marginHorizontal: theme.spacing.large }]}
          onPress={() => navigation.navigate('RuntimeGraphs')}
        >
          <Text style={commonStyles.buttonText}>View Detailed Graphs</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  batteryContainer: {
    alignItems: 'center',
    marginVertical: theme.spacing.large,
    paddingHorizontal: theme.spacing.large,
  },
  batteryOutline: {
    width: '100%',
    height: 30,
    borderRadius: theme.borderRadius.medium,
    backgroundColor: theme.colors.cardBackground,
    padding: 3,
    borderWidth: 1,
    borderColor: theme.colors.border,
    overflow: 'hidden',
  },
  batteryFill: {
    height: '100%',
    borderRadius: theme.borderRadius.small,
  },
  batteryPercentage: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginTop: theme.spacing.small,
  },
  batteryStatus: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  healthIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.medium,
  },
  healthStatus: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: theme.spacing.small,
  },
  healthStatusText: {
    fontSize: 18,
    fontWeight: '600',
  },
  capacityContainer: {
    marginTop: theme.spacing.medium,
  },
  capacityBar: {
    width: '100%',
    height: 8,
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.pill,
    overflow: 'hidden',
    marginTop: theme.spacing.small,
  },
  capacityFill: {
    height: '100%',
  },
  capacityText: {
    fontSize: theme.typography.body.fontSize,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.small,
  },
  capacityDetails: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: theme.spacing.small,
  },
  statItem: {
    width: '48%',
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.small,
    padding: theme.spacing.medium,
    marginBottom: theme.spacing.medium,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  statUnit: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
});
