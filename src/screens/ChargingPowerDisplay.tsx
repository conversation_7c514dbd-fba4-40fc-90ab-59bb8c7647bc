import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Animated } from 'react-native';
import { getBatteryStats } from '../utils/powerUtils';
import { theme, commonStyles } from '../theme';
import { AD_IDS } from '../utils/Constants';
import { AdComponent } from '../components/ads/AdComponent';
import { TopBannerAd } from '../components/ads/TopBannerAd';
import { useAdStore } from '../store/adStore';
import PowerGauge from '../components/Battery/PowerGauge';
import ChargingDetails from '../components/Battery/ChargingDetails';

interface ChargingPowerDisplayProps {
  navigation: {
    navigate: (screen: string) => void;
  };
}

export default function ChargingPowerDisplay({ navigation }: ChargingPowerDisplayProps) {

  const { isAdFree } = useAdStore();


  return (
    <View style={styles.container}>
      <TopBannerAd bannerId={AD_IDS.BANNER.TOP_CHARGING_POWER} nativeId={AD_IDS.NATIVE.CHARGING_POWER} />
      <ScrollView
        contentContainerStyle={[
          styles.scrollContent,
          { justifyContent: isAdFree ? 'center' : 'space-between' },
        ]}
      >
        <Text style={commonStyles.screenTitle}>Battery Charger</Text>

        {/* Power Gauge */}
        <PowerGauge />
        <TopBannerAd bannerId={AD_IDS.BANNER.CHARGING_POWER_2} nativeId={AD_IDS.NATIVE.CHARGING_POWER_2} />
        {/* Stats Cards */}
        <ChargingDetails />
        

        {/* Navigation Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={commonStyles.button}
            onPress={() => navigation.navigate('BatteryStats')}
          >
            <Text style={commonStyles.buttonText}>Battery Statistics</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={commonStyles.buttonSecondary}
            onPress={() => navigation.navigate('RuntimeGraphs')}
          >
            <Text style={commonStyles.buttonTextSecondary}>View Graphs</Text>
          </TouchableOpacity>
        </View>

        <AdComponent
          nativeId={AD_IDS.NATIVE.CHARGING_POWER_3}
          bannerId={AD_IDS.BANNER.CHARGING_POWER_3}
          showAdMedia={true}
        />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    alignItems: 'center',
    padding: theme.spacing.large,
    paddingBottom: theme.spacing.xlarge,
  },
  
  buttonContainer: {
    width: '100%',
    marginVertical: theme.spacing.large,
  },
});
