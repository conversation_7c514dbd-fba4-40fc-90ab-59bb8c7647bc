import React, { useState, useEffect } from 'react';
import { View, ScrollView, Text, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';
import { NativeModules } from 'react-native';
import { theme, commonStyles } from '../theme';
import { AD_IDS } from '../utils/Constants';
import { AdComponent } from '../components/ads/AdComponent';
import { TopBannerAd } from '../components/ads/TopBannerAd';
import remoteConfig from '@react-native-firebase/remote-config';
import { useRemoveAds } from '../hooks/useRemoveAds';


const { BatteryModule } = NativeModules;
const screenWidth = Dimensions.get('window').width;

export default function RuntimeGraphs() {
  const [voltageData, setVoltageData] = useState(Array(20).fill(0));
  const [currentData, setCurrentData] = useState(Array(20).fill(0));
  const [powerData, setPowerData] = useState(Array(20).fill(0));
  const [temperatureData, setTemperatureData] = useState(Array(20).fill(0));
  const [activeTab, setActiveTab] = useState('voltage');
  const [fadeAnim] = useState(new Animated.Value(0));
  const { isAdFree, handleRemoveAds, productPrice } = useRemoveAds();
  const hideGraphs = remoteConfig().getValue('hideGraphs').asBoolean();
  const hideGraphsValues = !hideGraphs && !isAdFree;
  useEffect(() => {
    // Fade in animation when component mounts
    const fadeInAnimation = Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    });

    fadeInAnimation.start();

    const interval = setInterval(() => {
      BatteryModule.getBatteryStats()
        .then((stats: any) => {
          const voltage = stats.voltage;
          const current = stats.current / 1000;
          const power = voltage * current;

          setVoltageData(prev => [...prev.slice(-19), voltage]);
          setCurrentData(prev => [...prev.slice(-19), current]);
          setPowerData(prev => [...prev.slice(-19), power]);
          setTemperatureData(prev => [...prev.slice(-19), stats.temperature]);
        })
        .catch((error: any) => {
          console.error('Failed to get battery stats:', error);
        });
    }, 1000);

    return () => {
      clearInterval(interval);
      fadeInAnimation.stop();
    };
  }, [fadeAnim]);

  // Get chart configuration based on data type
  const getChartConfig = (dataType: string) => {
    let gradientFrom, lineColor, dotColor;

    switch(dataType) {
      case 'voltage':
        gradientFrom = theme.colors.primary;
        lineColor = 'rgba(67, 97, 238, 1)';
        dotColor = theme.colors.secondary;
        break;
      case 'current':
        gradientFrom = theme.colors.secondary;
        lineColor = 'rgba(60, 207, 207, 1)';
        dotColor = theme.colors.highlight;
        break;
      case 'power':
        gradientFrom = theme.colors.highlight;
        lineColor = 'rgba(114, 9, 183, 1)';
        dotColor = theme.colors.primary;
        break;
      case 'temperature':
        gradientFrom = '#F44336';
        lineColor = 'rgba(244, 67, 54, 1)';
        dotColor = '#FF9800';
        break;
      default:
        gradientFrom = theme.colors.primary;
        lineColor = 'rgba(67, 97, 238, 1)';
        dotColor = theme.colors.secondary;
    }

    return {
      backgroundGradientFrom: theme.colors.cardBackground,
      backgroundGradientTo: theme.colors.cardBackground,
      backgroundGradientFromOpacity: 1,
      backgroundGradientToOpacity: 1,
      decimalPlaces: 2,
      color: (opacity = 1) => `${lineColor}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
      labelColor: () => theme.colors.textSecondary,
      strokeWidth: 3,
      barPercentage: 0.5,
      useShadowColorFromDataset: false,
      fillShadowGradient: gradientFrom,
      fillShadowGradientOpacity: 0.3,
      propsForBackgroundLines: {
        strokeDasharray: '5, 5',
        strokeWidth: 1,
        stroke: theme.colors.border,
      },
      propsForDots: {
        r: '4',
        strokeWidth: '2',
        stroke: dotColor,
      },
    };
  };

  // Get current value for the active chart
  const getCurrentValue = () => {
    switch(activeTab) {
      case 'voltage':
        return voltageData[voltageData.length - 1]?.toFixed(2) + ' V';
      case 'current':
        return currentData[currentData.length - 1]?.toFixed(2) + ' A';
      case 'power':
        return powerData[powerData.length - 1]?.toFixed(2) + ' W';
      case 'temperature':
        return temperatureData[temperatureData.length - 1]?.toFixed(1) + ' °C';
      default:
        return '0';
    }
  };

  // Get data for the active chart
  const getActiveData = () => {
    // Show placeholder data if locked
    if (hideGraphsValues) {
      return Array(20).fill(0);
    }

    switch(activeTab) {
      case 'voltage': return voltageData;
      case 'current': return currentData;
      case 'power': return powerData;
      case 'temperature': return temperatureData;
      default: return voltageData;
    }
  };

  // Get unit for the active chart
  const getActiveUnit = () => {
    switch(activeTab) {
      case 'voltage': return 'V';
      case 'current': return 'A';
      case 'power': return 'W';
      case 'temperature': return '°C';
      default: return '';
    }
  };

  // Handle tab change with animation
  const handleTabChange = (tab: string) => {
    const sequenceAnimation = Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]);

    sequenceAnimation.start();

    setActiveTab(tab);
  };

  return (
    <View style={styles.container}>
      <TopBannerAd bannerId={AD_IDS.BANNER.TOP_RUNTIME_GRAPHS} nativeId={AD_IDS.NATIVE.TOP_RUNTIME_GRAPHS} />
      <ScrollView contentContainerStyle={styles.contentContainer}>
        <Text style={commonStyles.screenTitle}>Runtime Graphs</Text>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'voltage' && styles.activeTab]}
          onPress={() => handleTabChange('voltage')}
        >
          <Text style={[styles.tabText, activeTab === 'voltage' && styles.activeTabText]}>Voltage</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'current' && styles.activeTab]}
          onPress={() => handleTabChange('current')}
        >
          <Text style={[styles.tabText, activeTab === 'current' && styles.activeTabText]}>Current</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'power' && styles.activeTab]}
          onPress={() => handleTabChange('power')}
        >
          <Text style={[styles.tabText, activeTab === 'power' && styles.activeTabText]}>Power</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'temperature' && styles.activeTab]}
          onPress={() => handleTabChange('temperature')}
        >
          <Text style={[styles.tabText, activeTab === 'temperature' && styles.activeTabText]}>Temp</Text>
        </TouchableOpacity>
      </View>

      <TopBannerAd bannerId={AD_IDS.BANNER.GRAPH_PAGE_2} nativeId={AD_IDS.NATIVE.GRAPH_PAGE_2} />

      {/* Premium Content Container */}
      <View style={styles.premiumContentContainer}>
        {/* Current Value Display */}
        <View style={styles.currentValueContainer}>
          <Text style={styles.currentValueLabel}>Current {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}</Text>
          <Text style={styles.currentValue}>
            {(hideGraphsValues) ? '••••' : getCurrentValue()}
          </Text>
        </View>

        {/* Chart */}
        <Animated.View style={[styles.chartContainer, { opacity: fadeAnim }]}>
          <LineChart
            data={{
              labels: ['', '', '', '', '', ''],
              datasets: [{
                data: getActiveData(),
                color: (opacity = 1) => getChartConfig(activeTab).color(opacity),
                strokeWidth: 3,
              }],
            }}
            width={screenWidth - 40}
            height={280}
            yAxisSuffix={getActiveUnit()}
            yAxisInterval={1}
            chartConfig={getChartConfig(activeTab)}
            bezier
            style={styles.chart}
            withInnerLines={true}
            withOuterLines={false}
            withVerticalLines={false}
            withHorizontalLines={true}
            withVerticalLabels={false}
            withHorizontalLabels={true}
            fromZero={activeTab !== 'voltage'}
            segments={5}
          />
        </Animated.View>

        {/* Blur Overlay for Premium Feature */}
        {hideGraphsValues && (
          <View style={styles.blurOverlay}>
            <View style={styles.unlockContainer}>
              <Text style={styles.unlockTitle}>🔒 Premium Feature</Text>
              <Text style={styles.unlockDescription}>
                Unlock detailed runtime graphs and remove all ads
              </Text>
              <TouchableOpacity
                style={styles.unlockButton}
                onPress={handleRemoveAds}
                activeOpacity={0.8}
              >
                <Text style={styles.unlockButtonText}>🔓 Unlock for {productPrice}</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

      {/* Stats Cards */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.statsScrollView}>
        <View style={styles.statsCard}>
          <Text style={styles.statsLabel}>Min</Text>
          <Text style={styles.statsValue}>
            {(hideGraphsValues) ? '••••' : `${Math.min(...getActiveData().filter(val => val > 0)).toFixed(2)} ${getActiveUnit()}`}
          </Text>
        </View>

        <View style={styles.statsCard}>
          <Text style={styles.statsLabel}>Max</Text>
          <Text style={styles.statsValue}>
            {(hideGraphsValues) ? '••••' : `${Math.max(...getActiveData()).toFixed(2)} ${getActiveUnit()}`}
          </Text>
        </View>

        <View style={styles.statsCard}>
          <Text style={styles.statsLabel}>Avg</Text>
          <Text style={styles.statsValue}>
            {(hideGraphsValues) ? '••••' : `${(getActiveData().reduce((a, b) => a + b, 0) / getActiveData().filter(val => val > 0).length).toFixed(2)} ${getActiveUnit()}`}
          </Text>
        </View>
      </ScrollView>

      </View>
      <AdComponent
        nativeId={AD_IDS.NATIVE.GRAPH_PAGE_1}
        bannerId={AD_IDS.BANNER.GRAPH_PAGE_1}
        showAdMedia={true}
      />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  contentContainer: {
    padding: theme.spacing.medium,
    paddingBottom: theme.spacing.xlarge,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.pill,
    marginVertical: theme.spacing.medium,
    padding: theme.spacing.xsmall,
    ...theme.shadow.small,
  },
  tab: {
    flex: 1,
    paddingVertical: theme.spacing.small,
    alignItems: 'center',
    borderRadius: theme.borderRadius.pill,
  },
  activeTab: {
    backgroundColor: theme.colors.primary,
    ...theme.shadow.small,
  },
  tabText: {
    color: theme.colors.textSecondary,
    fontSize: 14,
    fontWeight: '500',
  },
  activeTabText: {
    color: theme.colors.text,
    fontWeight: '600',
  },
  premiumContentContainer: {
    position: 'relative',
  },
  currentValueContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.medium,
  },
  currentValueLabel: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xsmall,
  },
  currentValue: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.text,
  },
  chartContainer: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.medium,
    marginBottom: theme.spacing.medium,
    ...theme.shadow.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  chart: {
    borderRadius: theme.borderRadius.medium,
    paddingRight: theme.spacing.small,
  },
  statsScrollView: {
    marginBottom: theme.spacing.medium,
  },
  statsCard: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.medium,
    marginRight: theme.spacing.medium,
    width: 100,
    alignItems: 'center',
    ...theme.shadow.small,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  statsLabel: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.small,
  },
  statsValue: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.text,
  },
  blurOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: theme.borderRadius.medium,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  unlockContainer: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.large,
    alignItems: 'center',
    maxWidth: 280,
    ...theme.shadow.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  unlockTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing.small,
    textAlign: 'center',
  },
  unlockDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.large,
    lineHeight: 20,
  },
  unlockButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.large,
    paddingVertical: theme.spacing.medium,
    borderRadius: theme.borderRadius.pill,
    ...theme.shadow.medium,
  },
  unlockButtonText: {
    color: theme.colors.text,
    fontSize: 16,
    fontWeight: '600',
  },
});
