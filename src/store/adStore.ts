import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';

interface AdState {
  isAdFree: boolean;
  isFirstLaunch: boolean;
  setAdFree: (value: boolean) => void;
  setFirstLaunch: (value: boolean) => void;
  initializeAdState: () => Promise<void>;
}

export const useAdStore = create<AdState>((set) => {
  const  setFirstLaunch = (value: boolean): void => {
    set({ isFirstLaunch: value });
    AsyncStorage.setItem('isFirstLaunch', JSON.stringify(value));
  };
  return {
  isAdFree: false,
  isFirstLaunch: true,
  setAdFree: (value: boolean) => {
    set({ isAdFree: value });
    AsyncStorage.setItem('isAdFree', JSON.stringify(value));
  },
  setFirstLaunch,
  initializeAdState: async () => {
    try {
      const adFreeValue = await AsyncStorage.getItem('isAdFree');
      const firstLaunchValue = await AsyncStorage.getItem('isFirstLaunch');

      if (adFreeValue !== null) {
        set({ isAdFree: JSON.parse(adFreeValue) });
      }

      if (firstLaunchValue !== null) {
        // App has been launched before
        console.log('=============App has been launched before');
        setFirstLaunch( false );
      } else {
        console.log('=============App is launching for the first time');
        // First time opening the app
        setFirstLaunch(true);
      }
    } catch (error) {
      set({ isAdFree: false, isFirstLaunch: true });
      console.error('Error loading ad state:', error);
    }
  },
}});
